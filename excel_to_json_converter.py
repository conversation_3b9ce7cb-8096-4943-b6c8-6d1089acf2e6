#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel转JSON转换器
将"审核问答对.xlsx"文件中的数据转换为单独的JSON文件
"""

import pandas as pd
import json
import os
from pathlib import Path
import sys

def clean_column_names(df):
    """清理列名，移除未命名的列"""
    # 定义期望的列名
    expected_columns = ['类别', '问题', '回答', '分类', '优化后的回答', '是否可用', '原因']
    
    # 获取实际的有用列
    useful_columns = []
    for col in df.columns:
        if not col.startswith('Unnamed'):
            useful_columns.append(col)
    
    print(f"检测到的有用列: {useful_columns}")
    return useful_columns

def convert_excel_to_json(excel_file, output_dir='json_output'):
    """
    将Excel文件转换为JSON文件
    
    Args:
        excel_file (str): Excel文件路径
        output_dir (str): 输出目录
    """
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_file}")
        df = pd.read_excel(excel_file)
        
        print(f"文件读取成功，共 {len(df)} 行数据")
        
        # 清理列名，只保留有用的列
        useful_columns = clean_column_names(df)
        df_clean = df[useful_columns].copy()
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        print(f"输出目录: {output_path.absolute()}")
        
        # 转换每一行数据
        success_count = 0
        for index, row in df_clean.iterrows():
            try:
                # 创建JSON数据，将NaN值替换为None
                json_data = {}
                for col in useful_columns:
                    value = row[col]
                    # 处理NaN值和空值
                    if pd.isna(value) or value == '' or (isinstance(value, str) and value.strip() == ''):
                        json_data[col] = None
                    else:
                        json_data[col] = str(value).strip() if isinstance(value, str) else value
                
                # 生成文件名（3位数字，从001开始）
                filename = f"{index + 1:03d}.json"
                filepath = output_path / filename
                
                # 写入JSON文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
                success_count += 1
                print(f"已处理第 {index + 1} 行 -> {filename}")
                
            except Exception as e:
                print(f"处理第 {index + 1} 行时出错: {e}")
                continue
        
        print(f"\n转换完成！")
        print(f"成功转换 {success_count} 个文件")
        print(f"输出目录: {output_path.absolute()}")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{excel_file}'")
        return False
    except Exception as e:
        print(f"转换过程中出错: {e}")
        return False

def main():
    """主函数"""
    excel_file = "审核问答对.xlsx"
    output_dir = "json_output"
    
    print("=" * 60)
    print("Excel转JSON转换器")
    print("=" * 60)
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 '{excel_file}' 不存在")
        return
    
    # 执行转换
    success = convert_excel_to_json(excel_file, output_dir)
    
    if success:
        print("\n转换成功完成！")
        
        # 显示输出目录中的文件
        output_path = Path(output_dir)
        json_files = list(output_path.glob("*.json"))
        print(f"\n生成的JSON文件数量: {len(json_files)}")
        
        if json_files:
            print("\n前5个文件示例:")
            for i, file in enumerate(sorted(json_files)[:5]):
                print(f"  {file.name}")
    else:
        print("\n转换失败！")

if __name__ == "__main__":
    main()
