@echo off
chcp 65001 >nul
echo ========================================
echo 银行客服问答对合规评估系统
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import openai" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包 openai...
    pip install openai
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 选择运行模式：
echo 1. 批量评估所有文件
echo 2. 测试单个文件
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 开始批量评估...
    python compliance_evaluator.py
    echo.
    echo 评估完成！结果已保存到 evaluation_results 文件夹
) else if "%choice%"=="2" (
    echo.
    echo 开始单文件测试...
    python test_compliance_single.py
    echo.
    echo 测试完成！
) else if "%choice%"=="3" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，请重新运行程序
)

echo.
pause
