#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量合规评估脚本 - 一对一保存模式
为每个问答对文件生成对应的独立评估结果文件
"""

import os
import json
from datetime import datetime
from compliance_evaluator import ComplianceEvaluator
from config import API_CONFIG, PATH_CONFIG

def main():
    """主函数 - 批量处理并为每个问答对保存独立结果"""
    
    print("=" * 80)
    print("银行客服问答对合规评估 - 批量处理（一对一保存模式）")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"输入文件夹: {PATH_CONFIG['qa_folder']}")
    print(f"输出文件夹: {PATH_CONFIG['output_folder']}")
    print()
    
    # 创建评估器
    evaluator = ComplianceEvaluator()
    
    # 创建输出文件夹
    os.makedirs(PATH_CONFIG["output_folder"], exist_ok=True)
    
    # 生成汇总结果文件名（可选）
    summary_output_file = os.path.join(
        PATH_CONFIG["output_folder"],
        f"compliance_evaluation_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    
    print("开始批量评估...")
    print("注意：每个问答对将生成独立的结果文件")
    print("-" * 60)
    
    # 批量处理（启用一对一保存）
    results = evaluator.process_qa_folder(
        folder_path=PATH_CONFIG["qa_folder"],
        output_file=summary_output_file,  # 汇总结果文件
        save_individual=True  # 启用一对一保存
    )
    
    print()
    print("=" * 80)
    print("批量评估完成！")
    print("=" * 80)
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总处理文件数: {len(results)}")
    print(f"汇总结果文件: {summary_output_file}")
    print(f"单个结果文件夹: {PATH_CONFIG['output_folder']}")
    print()
    
    # 显示生成的单个结果文件列表
    print("生成的单个结果文件:")
    print("-" * 40)
    result_files = [f for f in os.listdir(PATH_CONFIG['output_folder']) if f.endswith('_result.json')]
    result_files.sort()
    
    for i, filename in enumerate(result_files, 1):
        original_name = filename.replace('_result.json', '.json')
        print(f"{i:3d}. {original_name} → {filename}")
    
    print()
    print("文件命名规则说明:")
    print("  输入文件: 问答对/XXX.json")
    print("  输出文件: evaluation_results/XXX_result.json")
    print()
    print("您可以使用 result_viewer.py 查看具体的评估结果")

if __name__ == "__main__":
    main()
