# PowerShell脚本：激活conda环境并运行合规评估
# 适用于miniconda安装在 D:\ProgramData\miniconda3 的情况

Write-Host "正在初始化conda环境..." -ForegroundColor Green

# 设置conda路径
$condaPath = "D:\ProgramData\miniconda3"
$condaExe = "$condaPath\Scripts\conda.exe"
$condaActivate = "$condaPath\Scripts\activate.bat"

# 检查conda是否存在
if (-not (Test-Path $condaExe)) {
    Write-Host "错误：找不到conda.exe在路径 $condaExe" -ForegroundColor Red
    Write-Host "请确认miniconda安装路径是否正确" -ForegroundColor Red
    exit 1
}

Write-Host "找到conda: $condaExe" -ForegroundColor Green

# 检查环境是否存在
Write-Host "检查conda环境..." -ForegroundColor Yellow
$envList = & $condaExe env list 2>$null
if ($envList -match "compliance_envs") {
    Write-Host "找到环境: compliance_envs" -ForegroundColor Green
} else {
    Write-Host "环境 compliance_envs 不存在，正在创建..." -ForegroundColor Yellow
    
    # 创建新环境
    Write-Host "创建conda环境 compliance_envs (Python 3.10)..." -ForegroundColor Yellow
    & $condaExe create -n compliance_envs python=3.10 -y
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "创建环境失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "环境创建成功" -ForegroundColor Green
}

# 激活环境并安装依赖
Write-Host "激活环境并安装依赖..." -ForegroundColor Yellow

# 使用conda run来在指定环境中执行命令
Write-Host "安装openai库..." -ForegroundColor Yellow
& $condaExe run -n compliance_envs pip install openai

if ($LASTEXITCODE -ne 0) {
    Write-Host "安装openai失败" -ForegroundColor Red
    exit 1
}

Write-Host "依赖安装完成" -ForegroundColor Green

# 运行测试
Write-Host "运行合规评估测试..." -ForegroundColor Yellow
& $condaExe run -n compliance_envs python test_compliance_single.py

Write-Host "脚本执行完成" -ForegroundColor Green
