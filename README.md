# 银行客服问答对合规评估系统

基于qwen2.5-32b-instruct模型的银行客服问答对合规性评估工具。

## 功能特性

- **智能合规评估**: 使用先进的大语言模型进行多角度合规性分析
- **批量处理**: 支持批量处理问答对文件夹中的所有JSON文件
- **多维度评估**: 从信息披露、误导性营销、条款违反、格式规范等角度进行评估
- **详细报告**: 生成详细的评估结果和汇总报告
- **配置灵活**: 支持自定义API配置、评估标准和输出格式

## 文件结构

```
compliance_project/
├── compliance_evaluator.py       # 主评估脚本
├── batch_compliance_evaluator.py # 批量处理脚本（增强版）
├── test_compliance_single.py     # 单文件测试脚本
├── result_viewer.py              # 结果查看器
├── config.py                     # 配置文件
├── run_compliance_evaluation.bat # Windows批处理脚本
├── README.md                     # 使用说明
├── 问答对/                       # 问答对数据文件夹
│   ├── 001.json
│   ├── 002.json
│   └── ...
└── evaluation_results/           # 评估结果输出文件夹
```

## 安装依赖

```bash
pip install openai
```

## 配置说明

在 `config.py` 文件中配置以下参数：

### API配置
- `api_key`: API密钥
- `base_url`: API基础URL
- `model`: 使用的模型名称

### 路径配置
- `qa_folder`: 问答对文件夹路径
- `output_folder`: 输出结果文件夹
- `log_file`: 日志文件路径

### 模型参数
- `temperature`: 温度参数（0.0-1.0）
- `max_tokens`: 最大输出token数
- `request_delay`: 请求间隔时间（秒）

## 使用方法

### 1. 快速开始（Windows用户）

```bash
run_compliance_evaluation.bat
```

运行批处理脚本，自动检查环境并提供菜单选择。

### 2. 批量评估所有文件

```bash
python compliance_evaluator.py
```

基础批量处理，处理 `问答对` 文件夹中的所有JSON文件。

### 3. 增强批量评估（推荐）

```bash
python batch_compliance_evaluator.py
```

增强版批量处理，支持：
- 进度显示和断点续传
- 错误恢复和重试
- 交互式菜单操作
- 详细进度报告

### 4. 测试单个文件

```bash
python test_compliance_single.py
```

测试单个文件的评估功能，验证系统是否正常工作。

### 5. 查看评估结果

```bash
python result_viewer.py
```

交互式结果查看器，支持：
- 结果摘要统计
- 关键词搜索
- 详细结果查看
- 结果筛选和导出

## 输入数据格式

问答对JSON文件应包含以下字段：

```json
{
  "类别": "票据",
  "问题": "客户问题内容",
  "回答": "客服回答内容",
  "分类": "业务分类",
  "优化后的回答": "优化建议",
  "是否可用": "可用/不可用",
  "原因": "不可用原因"
}
```

## 输出结果格式

评估结果包含以下信息：

```json
{
  "文件名": "001.json",
  "原始数据": {
    "类别": "票据",
    "问题": "...",
    "回答": "..."
  },
  "评估时间": "2024-01-01 12:00:00",
  "合规评估结果": "详细的评估分析结果",
  "评估状态": "成功"
}
```

## 合规评估标准

系统基于以下标准进行评估：

1. **相关性**: 回答必须有效解决客户信息需求
2. **准确性**: 不得有误导性或错误的业务知识
3. **格式一致性**: 语言结构清晰连贯
4. **操作清晰性**: 为客户行动提供明确指导
5. **语言质量**: 无严重语义错误或拼写错误
6. **长度限制**: 优选160字符以下，必要时拆分为多个意图
7. **内容原则**: 准确、易懂、简洁、明确、流畅
8. **用词标准**: 特定格式和术语要求

## 评估角度

模型从以下角度进行多轮推理：

- 信息披露是否充分
- 是否存在误导性营销或模糊表达
- 是否违反具体条款
- 格式条款、公平交易、产品属性混淆等角度

## 日志和错误处理

- 系统会生成详细的日志文件 `compliance_evaluation.log`
- 包含完整的处理过程和错误信息
- 支持断点续传和错误恢复

## 注意事项

1. **API限制**: 系统内置请求延迟机制，避免触发API限制
2. **文件格式**: 确保输入文件为有效的JSON格式
3. **网络连接**: 需要稳定的网络连接访问API服务
4. **存储空间**: 确保有足够的磁盘空间存储评估结果

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API服务是否可用

2. **文件读取错误**
   - 确认文件路径正确
   - 检查文件格式是否为有效JSON
   - 验证文件编码为UTF-8

3. **内存不足**
   - 减少批处理文件数量
   - 增加系统内存
   - 优化模型参数设置

## 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 配置文件设置是否正确
3. 输入数据格式是否符合要求

## 版本信息

- 版本: 1.0.0
- 支持的模型: qwen2.5-32b-instruct
- Python版本要求: 3.7+
