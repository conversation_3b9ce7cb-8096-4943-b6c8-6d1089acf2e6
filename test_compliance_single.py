#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个文件合规评估测试脚本
用于测试合规评估功能
"""

import json
from compliance_evaluator import ComplianceEvaluator
from datetime import datetime

def test_single_file():
    """测试单个文件的合规评估"""
    
    # API配置
    API_KEY = "sk-XrH4scuZIrUNZ7FRMMhu4Dt86sEhLEMSz2GTiSPt2TAIaZm4"
    BASE_URL = "https://api.qingyuntop.top/v1"
    
    # 创建评估器
    evaluator = ComplianceEvaluator(API_KEY, BASE_URL)
    
    # 测试文件路径
    test_file = "问答对/002.json"  # 选择一个可用的文件进行测试
    
    print("=" * 60)
    print("银行客服问答对合规评估 - 单文件测试")
    print("=" * 60)
    print(f"测试文件: {test_file}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 处理单个文件
    result = evaluator.process_single_file(test_file)
    
    if result:
        print("原始问答对数据:")
        print("-" * 40)
        original_data = result["原始数据"]
        print(f"类别: {original_data.get('类别', 'N/A')}")
        print(f"问题: {original_data.get('问题', 'N/A')}")
        print(f"回答: {original_data.get('回答', 'N/A')}")
        print(f"分类: {original_data.get('分类', 'N/A')}")
        print(f"是否可用: {original_data.get('是否可用', 'N/A')}")
        print()
        
        print("合规评估结果:")
        print("-" * 40)
        print(result["合规评估结果"])
        print()
        
        # 保存测试结果
        output_file = f"test_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"测试结果已保存到: {output_file}")
        
    else:
        print("测试失败：无法处理指定文件")

if __name__ == "__main__":
    test_single_file()
