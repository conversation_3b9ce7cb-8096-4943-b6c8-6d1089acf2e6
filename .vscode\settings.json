{"terminal.integrated.env.windows": {"PYTHONPATH": ""}, "python.terminal.activateEnvironment": false, "python.terminal.activateEnvInCurrentTerminal": false, "terminal.integrated.inheritEnv": false, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "args": ["-NoProfile", "-ExecutionPolicy", "Bypass"]}}, "terminal.integrated.defaultProfile.windows": "PowerShell", "python.defaultInterpreterPath": "D:\\Anaconda3\\python.exe"}