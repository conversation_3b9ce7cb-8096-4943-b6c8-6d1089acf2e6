@echo off
echo ========================================
echo Banking Customer Service QA Compliance Evaluation
echo ========================================
echo.

REM Set conda path
set CONDA_PATH=D:\ProgramData\miniconda3
set CONDA_EXE=%CONDA_PATH%\Scripts\conda.exe

echo Checking conda installation...
if not exist "%CONDA_EXE%" (
    echo Error: Cannot find conda.exe at path %CONDA_EXE%
    echo Please confirm miniconda installation path is correct
    pause
    exit /b 1
)

echo Found conda: %CONDA_EXE%
echo.

echo Checking if environment exists...
"%CONDA_EXE%" env list | findstr "compliance_envs" >nul
if %errorlevel% neq 0 (
    echo Environment compliance_envs does not exist, creating...
    "%CONDA_EXE%" create -n compliance_envs python=3.10 -y
    if %errorlevel% neq 0 (
        echo Failed to create environment
        pause
        exit /b 1
    )
    echo Environment created successfully
) else (
    echo Found environment: compliance_envs
)

echo.
echo Installing dependencies...
"%CONDA_EXE%" run -n compliance_envs pip install openai
if %errorlevel% neq 0 (
    echo Failed to install openai
    pause
    exit /b 1
)

echo.
echo Dependencies installed, starting test...
echo ========================================
echo.

"%CONDA_EXE%" run -n compliance_envs python test_compliance_single.py

echo.
echo ========================================
echo Test completed
echo ========================================
pause
