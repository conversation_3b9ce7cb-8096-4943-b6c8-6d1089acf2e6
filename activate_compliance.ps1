# PowerShell script to activate conda environment
# This script initializes conda and activates the compliance_envs environment

Write-Host "Initializing conda..." -ForegroundColor Yellow

# Set conda environment variables
$Env:CONDA_EXE = "D:\ProgramData\miniconda3\Scripts\conda.exe"
$Env:_CE_M = ""
$Env:_CE_CONDA = ""
$Env:_CONDA_ROOT = "D:\ProgramData\miniconda3"
$Env:_CONDA_EXE = "D:\ProgramData\miniconda3\Scripts\conda.exe"

try {
    # Import conda module
    $CondaModuleArgs = @{ChangePs1 = $True }
    Import-Module "$Env:_CONDA_ROOT\shell\condabin\Conda.psm1" -ArgumentList $CondaModuleArgs
    
    Write-Host "Conda module imported successfully" -ForegroundColor Green
    
    # Activate the compliance_envs environment
    conda activate compliance_envs
    
    Write-Host "Environment 'compliance_envs' activated successfully!" -ForegroundColor Green
    Write-Host "You can now use conda commands and Python from the activated environment." -ForegroundColor Cyan
    
    # Clean up
    Remove-Variable CondaModuleArgs
    
}
catch {
    Write-Error "Failed to initialize conda: $($_.Exception.Message)"
    Write-Host "Trying alternative method..." -ForegroundColor Yellow
    
    # Alternative method using conda hook
    try {
        $condaHook = & "D:\ProgramData\miniconda3\Scripts\conda.exe" "shell.powershell" "hook"
        if ($condaHook) {
            $condaHook | Out-String | Where-Object { $_ } | Invoke-Expression
            conda activate compliance_envs
            Write-Host "Environment activated using alternative method!" -ForegroundColor Green
        }
    }
    catch {
        Write-Error "Alternative method also failed: $($_.Exception.Message)"
    }
}
