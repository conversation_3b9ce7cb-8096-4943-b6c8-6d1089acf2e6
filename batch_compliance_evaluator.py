#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量合规评估脚本 - 增强版
支持进度显示、断点续传、错误恢复等功能
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import logging
from compliance_evaluator import ComplianceEvaluator
from config import PATH_CONFIG

class BatchComplianceEvaluator:
    """批量合规评估器 - 增强版"""
    
    def __init__(self):
        self.evaluator = ComplianceEvaluator()
        self.progress_file = "evaluation_progress.json"
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('batch_evaluation.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def load_progress(self) -> Dict[str, Any]:
        """加载进度信息"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.warning(f"无法加载进度文件: {e}")
        return {"completed_files": [], "failed_files": [], "last_update": None}
    
    def save_progress(self, progress: Dict[str, Any]):
        """保存进度信息"""
        try:
            progress["last_update"] = datetime.now().isoformat()
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存进度失败: {e}")
    
    def get_file_list(self, folder_path: str) -> List[str]:
        """获取待处理文件列表"""
        if not os.path.exists(folder_path):
            logging.error(f"文件夹不存在: {folder_path}")
            return []
        
        json_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
        json_files.sort()
        return json_files
    
    def display_progress(self, current: int, total: int, filename: str):
        """显示进度"""
        percentage = (current / total) * 100
        bar_length = 50
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f'\r进度: |{bar}| {percentage:.1f}% ({current}/{total}) - {filename}', end='', flush=True)
    
    def process_with_resume(self, folder_path: str, output_file: str = None) -> List[Dict[str, Any]]:
        """支持断点续传的批量处理"""
        # 获取文件列表
        all_files = self.get_file_list(folder_path)
        if not all_files:
            return []
        
        # 加载进度
        progress = self.load_progress()
        completed_files = set(progress.get("completed_files", []))
        failed_files = set(progress.get("failed_files", []))
        
        # 过滤已完成的文件
        remaining_files = [f for f in all_files if f not in completed_files]
        
        logging.info(f"总文件数: {len(all_files)}")
        logging.info(f"已完成: {len(completed_files)}")
        logging.info(f"失败文件: {len(failed_files)}")
        logging.info(f"待处理: {len(remaining_files)}")
        
        if not remaining_files:
            logging.info("所有文件已处理完成！")
            return []
        
        # 询问是否继续处理失败的文件
        if failed_files:
            retry_failed = input(f"\n发现 {len(failed_files)} 个失败文件，是否重新处理？(y/n): ").lower() == 'y'
            if retry_failed:
                remaining_files.extend(list(failed_files))
                failed_files.clear()
        
        all_results = []
        
        print(f"\n开始处理 {len(remaining_files)} 个文件...")
        
        for i, filename in enumerate(remaining_files, 1):
            file_path = os.path.join(folder_path, filename)
            
            try:
                # 显示进度
                self.display_progress(i, len(remaining_files), filename)
                
                # 处理文件
                result = self.evaluator.process_single_file(file_path)
                
                if result:
                    all_results.append(result)
                    completed_files.add(filename)
                    failed_files.discard(filename)  # 从失败列表中移除
                else:
                    failed_files.add(filename)
                    logging.warning(f"处理失败: {filename}")
                
                # 更新进度
                progress["completed_files"] = list(completed_files)
                progress["failed_files"] = list(failed_files)
                self.save_progress(progress)
                
                # 添加延迟
                time.sleep(self.evaluator.model_config["request_delay"])
                
            except KeyboardInterrupt:
                print(f"\n\n用户中断处理，已保存进度。")
                print(f"已完成: {len(completed_files)} 个文件")
                print(f"可使用相同命令继续处理剩余文件。")
                break
                
            except Exception as e:
                logging.error(f"处理文件 {filename} 时出错: {e}")
                failed_files.add(filename)
                progress["failed_files"] = list(failed_files)
                self.save_progress(progress)
                continue
        
        print(f"\n\n处理完成！")
        print(f"成功: {len(completed_files)} 个文件")
        print(f"失败: {len(failed_files)} 个文件")
        
        # 保存结果
        if output_file and all_results:
            self.evaluator.save_results(all_results, output_file)
        
        return all_results
    
    def generate_final_report(self):
        """生成最终报告"""
        progress = self.load_progress()
        completed_files = progress.get("completed_files", [])
        failed_files = progress.get("failed_files", [])
        
        report = f"""
=== 批量合规评估最终报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

处理统计:
- 成功处理: {len(completed_files)} 个文件
- 失败处理: {len(failed_files)} 个文件
- 总成功率: {len(completed_files)/(len(completed_files)+len(failed_files))*100:.1f}%

失败文件列表:
{chr(10).join(f"- {f}" for f in failed_files) if failed_files else "无"}

建议:
1. 检查失败文件的格式是否正确
2. 确认网络连接稳定
3. 验证API配置是否正确
        """.strip()
        
        print(report)
        
        # 保存报告到文件
        with open("final_evaluation_report.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n最终报告已保存到: final_evaluation_report.txt")
    
    def clean_progress(self):
        """清理进度文件"""
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)
            print("进度文件已清理")


def main():
    """主函数"""
    print("=" * 60)
    print("银行客服问答对合规评估系统 - 批量处理")
    print("=" * 60)
    
    batch_evaluator = BatchComplianceEvaluator()
    
    # 创建输出文件夹
    os.makedirs(PATH_CONFIG["output_folder"], exist_ok=True)
    
    # 输出文件路径
    output_file = os.path.join(
        PATH_CONFIG["output_folder"],
        f"batch_evaluation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    
    print(f"输入文件夹: {PATH_CONFIG['qa_folder']}")
    print(f"输出文件: {output_file}")
    print()
    
    # 显示菜单
    while True:
        print("请选择操作:")
        print("1. 开始/继续批量评估")
        print("2. 查看当前进度")
        print("3. 生成最终报告")
        print("4. 清理进度文件")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            print("\n开始批量评估...")
            batch_evaluator.process_with_resume(PATH_CONFIG["qa_folder"], output_file)
            
        elif choice == "2":
            progress = batch_evaluator.load_progress()
            completed = len(progress.get("completed_files", []))
            failed = len(progress.get("failed_files", []))
            last_update = progress.get("last_update", "无")
            
            print(f"\n当前进度:")
            print(f"- 已完成: {completed} 个文件")
            print(f"- 失败: {failed} 个文件")
            print(f"- 最后更新: {last_update}")
            
        elif choice == "3":
            batch_evaluator.generate_final_report()
            
        elif choice == "4":
            confirm = input("确认清理进度文件？这将重新开始评估 (y/n): ")
            if confirm.lower() == 'y':
                batch_evaluator.clean_progress()
                
        elif choice == "5":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")
        
        print()


if __name__ == "__main__":
    main()
