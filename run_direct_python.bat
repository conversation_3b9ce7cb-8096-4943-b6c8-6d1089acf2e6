@echo off
echo ========================================
echo 银行客服问答对合规评估系统 (直接Python版本)
echo ========================================
echo.

echo 检查Python安装...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

python --version
echo.

echo 检查并安装依赖...
python -m pip install --upgrade pip
python -m pip install openai

if %errorlevel% neq 0 (
    echo 安装依赖失败
    pause
    exit /b 1
)

echo.
echo 依赖安装完成，开始运行测试...
echo ========================================
echo.

python test_compliance_single.py

echo.
echo ========================================
echo 测试完成
echo ========================================
pause
