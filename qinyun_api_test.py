import openai
import time

# 打印openai的版本
print(openai.__version__)

client = openai.OpenAI(
    base_url="https://api.qingyuntop.top/v1",
    api_key="sk-XrH4scuZIrUNZ7FRMMhu4Dt86sEhLEMSz2GTiSPt2TAIaZm4"
)

messages = [
    {"role": "system", "content": "You are a helpful assistant."}
]

while True:
    user_input = input("You: ")
    messages.append({"role": "user", "content": user_input})

    response = client.chat.completions.create(
        model="qwen2.5-32b-instruct",
        messages=messages,
        temperature=1
    )

    assistant_response = response.choices[0].message.content
    print(f"Assistant: {assistant_response}")

    messages.append({"role": "assistant", "content": assistant_response})
    time.sleep(1)