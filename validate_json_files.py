#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件验证器
验证生成的JSON文件是否有效且格式正确
"""

import json
import os
from pathlib import Path

def validate_json_files(json_dir='json_output'):
    """验证JSON文件"""
    json_path = Path(json_dir)
    
    if not json_path.exists():
        print(f"错误: 目录 '{json_dir}' 不存在")
        return False
    
    json_files = sorted(json_path.glob("*.json"))
    
    if not json_files:
        print(f"错误: 在目录 '{json_dir}' 中没有找到JSON文件")
        return False
    
    print(f"开始验证 {len(json_files)} 个JSON文件...")
    print("=" * 50)
    
    valid_count = 0
    invalid_files = []
    expected_keys = ['类别', '问题', '回答', '分类', '优化后的回答', '是否可用', '原因']
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查是否包含所有期望的键
            missing_keys = []
            for key in expected_keys:
                if key not in data:
                    missing_keys.append(key)
            
            if missing_keys:
                print(f"❌ {json_file.name}: 缺少键 {missing_keys}")
                invalid_files.append(json_file.name)
            else:
                valid_count += 1
                # 显示前5个文件的详细信息
                if valid_count <= 5:
                    print(f"✅ {json_file.name}: 有效")
                    for key, value in data.items():
                        if value is not None:
                            display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                            print(f"   {key}: {display_value}")
                        else:
                            print(f"   {key}: None")
                    print()
                elif valid_count % 20 == 0:
                    print(f"✅ 已验证 {valid_count} 个文件...")
                    
        except json.JSONDecodeError as e:
            print(f"❌ {json_file.name}: JSON格式错误 - {e}")
            invalid_files.append(json_file.name)
        except Exception as e:
            print(f"❌ {json_file.name}: 读取错误 - {e}")
            invalid_files.append(json_file.name)
    
    print("=" * 50)
    print(f"验证完成！")
    print(f"总文件数: {len(json_files)}")
    print(f"有效文件: {valid_count}")
    print(f"无效文件: {len(invalid_files)}")
    
    if invalid_files:
        print(f"无效文件列表: {invalid_files}")
        return False
    else:
        print("🎉 所有JSON文件都是有效的！")
        return True

def show_statistics(json_dir='json_output'):
    """显示统计信息"""
    json_path = Path(json_dir)
    json_files = sorted(json_path.glob("*.json"))
    
    print("\n" + "=" * 50)
    print("数据统计")
    print("=" * 50)
    
    categories = {}
    usable_count = 0
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 统计类别
            category = data.get('类别', '未知')
            categories[category] = categories.get(category, 0) + 1
            
            # 统计可用性
            if data.get('是否可用') == '可用':
                usable_count += 1
                
        except Exception as e:
            print(f"读取 {json_file.name} 时出错: {e}")
    
    print(f"按类别统计:")
    for category, count in sorted(categories.items()):
        print(f"  {category}: {count} 条")
    
    print(f"\n可用性统计:")
    print(f"  可用: {usable_count} 条")
    print(f"  不可用: {len(json_files) - usable_count} 条")
    print(f"  可用率: {usable_count/len(json_files)*100:.1f}%")

if __name__ == "__main__":
    print("JSON文件验证器")
    print("=" * 50)
    
    # 验证文件
    is_valid = validate_json_files()
    
    if is_valid:
        # 显示统计信息
        show_statistics()
