#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合规评估结果查看器
用于查看和分析评估结果
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any

class ResultViewer:
    """结果查看器"""
    
    def __init__(self):
        self.results_folder = "evaluation_results"
    
    def list_result_files(self) -> List[str]:
        """列出所有结果文件"""
        if not os.path.exists(self.results_folder):
            return []
        
        files = []
        for filename in os.listdir(self.results_folder):
            if filename.endswith('.json') and 'results' in filename:
                files.append(filename)
        
        return sorted(files, reverse=True)  # 按时间倒序
    
    def load_results(self, filename: str) -> List[Dict[str, Any]]:
        """加载评估结果"""
        file_path = os.path.join(self.results_folder, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载文件失败: {e}")
            return []
    
    def display_summary(self, results: List[Dict[str, Any]]):
        """显示结果摘要"""
        if not results:
            print("没有找到评估结果")
            return
        
        total = len(results)
        successful = len([r for r in results if r.get('评估状态') == '成功'])
        
        # 统计业务类别
        categories = {}
        for result in results:
            category = result.get('原始数据', {}).get('类别', '未知')
            categories[category] = categories.get(category, 0) + 1
        
        # 统计可用性
        availability = {}
        for result in results:
            avail = result.get('原始数据', {}).get('是否可用', '未知')
            availability[avail] = availability.get(avail, 0) + 1
        
        print("=" * 60)
        print("评估结果摘要")
        print("=" * 60)
        print(f"总文件数: {total}")
        print(f"成功评估: {successful}")
        print(f"成功率: {successful/total*100:.1f}%")
        print()
        
        print("业务类别分布:")
        for category, count in sorted(categories.items()):
            print(f"  {category}: {count} 个")
        print()
        
        print("原始可用性分布:")
        for avail, count in sorted(availability.items()):
            print(f"  {avail}: {count} 个")
        print()
    
    def search_by_keyword(self, results: List[Dict[str, Any]], keyword: str) -> List[Dict[str, Any]]:
        """按关键词搜索结果"""
        filtered_results = []
        keyword_lower = keyword.lower()
        
        for result in results:
            # 搜索问题、回答、评估结果
            original_data = result.get('原始数据', {})
            question = original_data.get('问题', '').lower()
            answer = original_data.get('回答', '').lower()
            evaluation = result.get('合规评估结果', '').lower()
            
            if (keyword_lower in question or 
                keyword_lower in answer or 
                keyword_lower in evaluation):
                filtered_results.append(result)
        
        return filtered_results
    
    def display_detailed_result(self, result: Dict[str, Any]):
        """显示详细结果"""
        print("=" * 80)
        print(f"文件: {result.get('文件名', 'Unknown')}")
        print("=" * 80)
        
        original_data = result.get('原始数据', {})
        print(f"业务类别: {original_data.get('类别', 'N/A')}")
        print(f"业务分类: {original_data.get('分类', 'N/A')}")
        print(f"原始可用性: {original_data.get('是否可用', 'N/A')}")
        print()
        
        print("客户问题:")
        print("-" * 40)
        print(original_data.get('问题', 'N/A'))
        print()
        
        print("客服回答:")
        print("-" * 40)
        print(original_data.get('回答', 'N/A'))
        print()
        
        print("合规评估结果:")
        print("-" * 40)
        print(result.get('合规评估结果', 'N/A'))
        print()
        
        print(f"评估时间: {result.get('评估时间', 'N/A')}")
        print(f"评估状态: {result.get('评估状态', 'N/A')}")
        print()
    
    def export_filtered_results(self, results: List[Dict[str, Any]], filename: str):
        """导出筛选后的结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"结果已导出到: {filename}")
        except Exception as e:
            print(f"导出失败: {e}")
    
    def interactive_viewer(self):
        """交互式查看器"""
        print("=" * 60)
        print("合规评估结果查看器")
        print("=" * 60)
        
        # 列出可用的结果文件
        result_files = self.list_result_files()
        if not result_files:
            print("未找到评估结果文件")
            return
        
        print("可用的结果文件:")
        for i, filename in enumerate(result_files, 1):
            print(f"{i}. {filename}")
        
        # 选择文件
        try:
            choice = int(input(f"\n请选择文件 (1-{len(result_files)}): ")) - 1
            if choice < 0 or choice >= len(result_files):
                print("无效选择")
                return
        except ValueError:
            print("无效输入")
            return
        
        selected_file = result_files[choice]
        print(f"\n加载文件: {selected_file}")
        
        results = self.load_results(selected_file)
        if not results:
            return
        
        current_results = results
        
        while True:
            print("\n" + "=" * 60)
            print("操作菜单:")
            print("1. 显示摘要统计")
            print("2. 按关键词搜索")
            print("3. 查看详细结果")
            print("4. 导出当前结果")
            print("5. 重置筛选")
            print("6. 返回文件选择")
            print("7. 退出")
            
            choice = input("\n请选择操作 (1-7): ").strip()
            
            if choice == "1":
                self.display_summary(current_results)
                
            elif choice == "2":
                keyword = input("请输入搜索关键词: ").strip()
                if keyword:
                    filtered = self.search_by_keyword(current_results, keyword)
                    print(f"\n找到 {len(filtered)} 个匹配结果")
                    current_results = filtered
                    
            elif choice == "3":
                if not current_results:
                    print("没有可显示的结果")
                    continue
                
                print(f"\n当前有 {len(current_results)} 个结果")
                try:
                    index = int(input(f"请选择要查看的结果 (1-{len(current_results)}): ")) - 1
                    if 0 <= index < len(current_results):
                        self.display_detailed_result(current_results[index])
                    else:
                        print("无效选择")
                except ValueError:
                    print("无效输入")
                    
            elif choice == "4":
                if current_results:
                    filename = f"filtered_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    self.export_filtered_results(current_results, filename)
                else:
                    print("没有可导出的结果")
                    
            elif choice == "5":
                current_results = results
                print("筛选已重置")
                
            elif choice == "6":
                break
                
            elif choice == "7":
                print("退出程序")
                return
                
            else:
                print("无效选择")


def main():
    """主函数"""
    viewer = ResultViewer()
    viewer.interactive_viewer()


if __name__ == "__main__":
    main()
