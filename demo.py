#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行客服问答对合规评估系统演示脚本
展示完整的评估流程
"""

import os
import json
from datetime import datetime
from compliance_evaluator import ComplianceEvaluator
from config import PATH_CONFIG

def demo_single_evaluation():
    """演示单个文件评估"""
    print("=" * 80)
    print("演示1: 单个文件合规评估")
    print("=" * 80)
    
    # 创建评估器
    evaluator = ComplianceEvaluator()
    
    # 选择一个示例文件
    demo_file = "问答对/002.json"
    
    print(f"正在评估文件: {demo_file}")
    print()
    
    # 加载并显示原始数据
    with open(demo_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print("原始问答对数据:")
    print("-" * 50)
    print(f"业务类别: {original_data.get('类别', 'N/A')}")
    print(f"客户问题: {original_data.get('问题', 'N/A')}")
    print(f"客服回答: {original_data.get('回答', 'N/A')}")
    print(f"业务分类: {original_data.get('分类', 'N/A')}")
    print(f"原始状态: {original_data.get('是否可用', 'N/A')}")
    print()
    
    # 进行评估
    print("正在进行合规评估...")
    result = evaluator.process_single_file(demo_file)
    
    if result:
        print("合规评估完成！")
        print()
        print("评估结果摘要:")
        print("-" * 50)
        evaluation_result = result.get('合规评估结果', '')
        
        # 提取关键信息
        if '不合规' in evaluation_result:
            compliance_status = "❌ 不合规"
        elif '合规' in evaluation_result:
            compliance_status = "✅ 合规"
        else:
            compliance_status = "⚠️ 需要进一步分析"
        
        print(f"合规状态: {compliance_status}")
        print(f"评估时间: {result.get('评估时间', 'N/A')}")
        
        # 显示部分评估详情
        lines = evaluation_result.split('\n')
        summary_lines = [line for line in lines if '判断' in line or '建议' in line or '原因' in line][:5]
        if summary_lines:
            print("\n关键评估要点:")
            for line in summary_lines:
                if line.strip():
                    print(f"  • {line.strip()}")
    else:
        print("❌ 评估失败")
    
    print("\n" + "=" * 80)

def demo_batch_preview():
    """演示批量处理预览"""
    print("演示2: 批量处理预览")
    print("=" * 80)
    
    qa_folder = PATH_CONFIG["qa_folder"]
    
    if not os.path.exists(qa_folder):
        print(f"❌ 问答对文件夹不存在: {qa_folder}")
        return
    
    # 获取文件列表
    json_files = [f for f in os.listdir(qa_folder) if f.endswith('.json')]
    json_files.sort()
    
    print(f"发现 {len(json_files)} 个问答对文件")
    print()
    
    # 分析文件内容
    categories = {}
    availability = {}
    sample_files = []
    
    for i, filename in enumerate(json_files[:10]):  # 只分析前10个文件作为示例
        file_path = os.path.join(qa_folder, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            category = data.get('类别', '未知')
            avail = data.get('是否可用', '未知')
            
            categories[category] = categories.get(category, 0) + 1
            availability[avail] = availability.get(avail, 0) + 1
            
            sample_files.append({
                'filename': filename,
                'category': category,
                'question': data.get('问题', '')[:50] + '...' if len(data.get('问题', '')) > 50 else data.get('问题', ''),
                'availability': avail
            })
            
        except Exception as e:
            print(f"⚠️ 读取文件 {filename} 失败: {e}")
    
    print("业务类别分布（前10个文件）:")
    for category, count in sorted(categories.items()):
        print(f"  {category}: {count} 个")
    print()
    
    print("可用性分布（前10个文件）:")
    for avail, count in sorted(availability.items()):
        print(f"  {avail}: {count} 个")
    print()
    
    print("文件示例:")
    print("-" * 80)
    for sample in sample_files[:5]:
        print(f"文件: {sample['filename']}")
        print(f"类别: {sample['category']} | 状态: {sample['availability']}")
        print(f"问题: {sample['question']}")
        print("-" * 40)
    
    print(f"\n预计评估时间: 约 {len(json_files) * 2} 分钟")
    print("（基于每个文件平均2分钟的处理时间）")
    
    print("\n" + "=" * 80)

def demo_configuration():
    """演示配置信息"""
    print("演示3: 系统配置信息")
    print("=" * 80)
    
    from config import API_CONFIG, MODEL_CONFIG, COMPLIANCE_CRITERIA
    
    print("API配置:")
    print(f"  模型: {API_CONFIG['model']}")
    print(f"  API地址: {API_CONFIG['base_url']}")
    print(f"  API密钥: {API_CONFIG['api_key'][:20]}...")
    print()
    
    print("模型参数:")
    print(f"  温度: {MODEL_CONFIG['temperature']}")
    print(f"  最大Token: {MODEL_CONFIG['max_tokens']}")
    print(f"  请求延迟: {MODEL_CONFIG['request_delay']} 秒")
    print()
    
    print("合规评估标准:")
    for i, (criterion, description) in enumerate(COMPLIANCE_CRITERIA.items(), 1):
        print(f"  {i}. {criterion}: {description}")
    print()
    
    print("文件路径配置:")
    print(f"  输入文件夹: {PATH_CONFIG['qa_folder']}")
    print(f"  输出文件夹: {PATH_CONFIG['output_folder']}")
    print(f"  日志文件: {PATH_CONFIG['log_file']}")
    
    print("\n" + "=" * 80)

def main():
    """主演示函数"""
    print("🏦 银行客服问答对合规评估系统")
    print("📊 系统演示和功能预览")
    print("⏰ " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print()
    
    while True:
        print("请选择演示内容:")
        print("1. 单个文件评估演示")
        print("2. 批量处理预览")
        print("3. 系统配置信息")
        print("4. 查看使用说明")
        print("5. 退出演示")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            demo_single_evaluation()
            
        elif choice == "2":
            demo_batch_preview()
            
        elif choice == "3":
            demo_configuration()
            
        elif choice == "4":
            print("\n" + "=" * 80)
            print("使用说明")
            print("=" * 80)
            print("1. 单文件测试: python test_compliance_single.py")
            print("2. 批量评估: python batch_compliance_evaluator.py")
            print("3. 结果查看: python result_viewer.py")
            print("4. Windows用户: 双击 run_compliance_evaluation.bat")
            print()
            print("详细说明请查看 README.md 文件")
            print("=" * 80)
            
        elif choice == "5":
            print("\n感谢使用银行客服问答对合规评估系统！")
            print("如有问题，请查看日志文件或联系技术支持。")
            break
            
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")
        print()


if __name__ == "__main__":
    main()
