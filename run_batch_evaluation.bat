@echo off
echo ========================================
echo Banking Customer Service QA Compliance Evaluation - Batch Mode
echo ========================================
echo.

echo Running batch evaluation with individual file saving...
echo Each QA pair will generate a separate result file.
echo.

D:\ProgramData\miniconda3\Scripts\conda.exe run -n compliance_envs python batch_individual_evaluation.py

echo.
echo ========================================
echo Batch evaluation completed
echo ========================================
pause
